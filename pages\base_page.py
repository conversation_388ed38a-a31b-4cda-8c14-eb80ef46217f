from playwright.sync_api import Page, expect

class BasePage:
    def __init__(self, page: Page):
        self.page = page

    def navigate(self, url: str):
        self.page.goto(url)
        return self

    def click(self, locator: str):
        self.page.locator(locator).click()
        return self

    def fill(self, locator: str, text: str):
        self.page.locator(locator).fill(text)
        return self

    def expect_element_to_have_text(self, locator: str, text: str):
        expect(self.page.locator(locator)).to_have_text(text)
        return self
