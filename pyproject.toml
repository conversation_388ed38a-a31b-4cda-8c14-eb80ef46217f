[project]
name = "playwright-python-template"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pytest",
    "pytest-playwright",
    "allure-pytest",
    "playwright",
    "faker",
    "ddddocr",
    "pyyaml",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "black",
    "mypy",
    "pre-commit",
]

[tool.ruff]
line-length = 88
select = ["E", "F", "W", "I"]

[tool.black]
line-length = 88

[tool.pytest.ini_options]
pythonpath = ["fixtures"]

